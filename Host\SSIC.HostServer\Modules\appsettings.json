{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning",
      "Aspire.Hosting.Dcp": "Warning"
    }
  },
  "AllowedHosts": "*",
  "Serilog": {
    "Using": [
      "Serilog.Sinks.Seq",
      "Serilog.Sinks.Console",
      "Serilog.Sinks.File",
      "Serilog.Expressions"
    ],
    "WriteTo": [
      {
        "Name": "Seq",
        "Args": {
          "serverUrl": "http://localhost:5341",
          "apiKey": "",
          "restrictedToMinimumLevel": "Information",
          "batchPostingLimit": 1000,
          "period": "00:00:02"
        }
      },
      {
        "Name": "Console",
        "Args": {
          "outputTemplate": "[{Timestamp:HH:mm:ss.fff} {Level:u3}] {Message:lj}{NewLine}{Exception}",
          "theme": "Serilog.Sinks.SystemConsole.Themes.AnsiConsoleTheme::Code, Serilog.Sinks.Console"
        }
      },
      {
        "Name": "File",
        "Args": {
          "path": "Logs/log_.json",
          "rollingInterval": "Day",
          "formatter": "Serilog.Formatting.Compact.CompactJsonFormatter, Serilog.Formatting.Compact",
          "retainedFileCountLimit": 31,
          "fileSizeLimitBytes": 104857600,
          "rollOnFileSizeLimit": true
        }
      }

    ],
    "Enrich": [
      "FromLogContext",
      "WithMachineName",
      "WithThreadId",
      "WithProcessId",
      "WithEnvironmentName",
      "WithAssemblyName",
      "WithMemoryUsage"
    ],
    "Properties": {
      "Application": "SSIC",
      "Environment": "Development"
    },
    "MinimumLevel": {
      "Default": "Information",
      "Override": {
        "System": "Warning",
        "Microsoft": "Warning",
        "Microsoft.Hosting.Lifetime": "Information",
        "Microsoft.EntityFrameworkCore": "Warning",
        "Microsoft.AspNetCore.Hosting.Diagnostics": "Warning",
        "Microsoft.AspNetCore.Mvc.Infrastructure": "Warning"
      }
    }
  },
  //"Dapr": {
  //  "GrpcPort": "50001",
  //  "HttpPort": "3500"
  //},
  "Zipkin": {
    "Endpoint": "http://localhost:9411/api/v2/spans"
  },
  "JwtConfig": {
    "secret": "U2FsdGVkX19FvLSuluqUFOjdSNLYu2iJOpoJSNOnbF8=",
    "expirationInMinutes": 1440
  },
  
    "OpenTelemetry": {
      "DOTNET_DASHBOARD_OTLP_ENDPOINT_URL": "https://localhost:4317",
      "DOTNET_RESOURCE_SERVICE_ENDPOINT_URL": "https://localhost:22090"
    }
  
}