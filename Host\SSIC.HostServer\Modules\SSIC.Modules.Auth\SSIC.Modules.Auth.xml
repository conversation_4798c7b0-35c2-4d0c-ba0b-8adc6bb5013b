<?xml version="1.0"?>
<doc>
    <assembly>
        <name>SSIC.Modules.Auth</name>
    </assembly>
    <members>
        <member name="T:SSIC.Modules.Auth.Controllers.AuthController">
            <summary>
            用户认证控制器
            </summary>
        </member>
        <member name="M:SSIC.Modules.Auth.Controllers.AuthController.Login(SSIC.Modules.Auth.Controllers.LoginRequest)">
            <summary>
            用户登录
            </summary>
            <param name="request">登录请求</param>
            <returns>登录结果</returns>
        </member>
        <member name="M:SSIC.Modules.Auth.Controllers.AuthController.Register(SSIC.Modules.Auth.Controllers.RegisterRequest)">
            <summary>
            用户注册
            </summary>
            <param name="request">注册请求</param>
            <returns>注册结果</returns>
        </member>
        <member name="M:SSIC.Modules.Auth.Controllers.AuthController.GetProfile">
            <summary>
            获取当前用户信息
            </summary>
            <returns>用户信息</returns>
        </member>
        <member name="M:SSIC.Modules.Auth.Controllers.AuthController.Logout">
            <summary>
            用户登出
            </summary>
            <returns>登出结果</returns>
        </member>
        <member name="M:SSIC.Modules.Auth.Controllers.AuthController.ChangePassword(SSIC.Modules.Auth.Controllers.ChangePasswordRequest)">
            <summary>
            修改密码
            </summary>
            <param name="request">修改密码请求</param>
            <returns>修改结果</returns>
        </member>
        <member name="M:SSIC.Modules.Auth.Controllers.AuthController.GenerateToken(System.String)">
            <summary>
            生成模拟Token
            </summary>
            <param name="username">用户名</param>
            <returns>Token</returns>
        </member>
        <member name="T:SSIC.Modules.Auth.Controllers.LoginRequest">
            <summary>
            登录请求模型
            </summary>
        </member>
        <member name="P:SSIC.Modules.Auth.Controllers.LoginRequest.Username">
            <summary>
            用户名
            </summary>
        </member>
        <member name="P:SSIC.Modules.Auth.Controllers.LoginRequest.Password">
            <summary>
            密码
            </summary>
        </member>
        <member name="P:SSIC.Modules.Auth.Controllers.LoginRequest.RememberMe">
            <summary>
            记住我
            </summary>
        </member>
        <member name="T:SSIC.Modules.Auth.Controllers.LoginResponse">
            <summary>
            登录响应模型
            </summary>
        </member>
        <member name="P:SSIC.Modules.Auth.Controllers.LoginResponse.Success">
            <summary>
            是否成功
            </summary>
        </member>
        <member name="P:SSIC.Modules.Auth.Controllers.LoginResponse.Message">
            <summary>
            消息
            </summary>
        </member>
        <member name="P:SSIC.Modules.Auth.Controllers.LoginResponse.Token">
            <summary>
            访问令牌
            </summary>
        </member>
        <member name="P:SSIC.Modules.Auth.Controllers.LoginResponse.Username">
            <summary>
            用户名
            </summary>
        </member>
        <member name="P:SSIC.Modules.Auth.Controllers.LoginResponse.ExpiresAt">
            <summary>
            过期时间
            </summary>
        </member>
        <member name="T:SSIC.Modules.Auth.Controllers.RegisterRequest">
            <summary>
            注册请求模型
            </summary>
        </member>
        <member name="P:SSIC.Modules.Auth.Controllers.RegisterRequest.Username">
            <summary>
            用户名
            </summary>
        </member>
        <member name="P:SSIC.Modules.Auth.Controllers.RegisterRequest.Password">
            <summary>
            密码
            </summary>
        </member>
        <member name="P:SSIC.Modules.Auth.Controllers.RegisterRequest.ConfirmPassword">
            <summary>
            确认密码
            </summary>
        </member>
        <member name="P:SSIC.Modules.Auth.Controllers.RegisterRequest.Email">
            <summary>
            邮箱
            </summary>
        </member>
        <member name="T:SSIC.Modules.Auth.Controllers.UserProfile">
            <summary>
            用户信息模型
            </summary>
        </member>
        <member name="P:SSIC.Modules.Auth.Controllers.UserProfile.Username">
            <summary>
            用户名
            </summary>
        </member>
        <member name="P:SSIC.Modules.Auth.Controllers.UserProfile.Email">
            <summary>
            邮箱
            </summary>
        </member>
        <member name="P:SSIC.Modules.Auth.Controllers.UserProfile.Role">
            <summary>
            角色
            </summary>
        </member>
        <member name="P:SSIC.Modules.Auth.Controllers.UserProfile.LastLoginTime">
            <summary>
            最后登录时间
            </summary>
        </member>
        <member name="T:SSIC.Modules.Auth.Controllers.ChangePasswordRequest">
            <summary>
            修改密码请求模型
            </summary>
        </member>
        <member name="P:SSIC.Modules.Auth.Controllers.ChangePasswordRequest.OldPassword">
            <summary>
            旧密码
            </summary>
        </member>
        <member name="P:SSIC.Modules.Auth.Controllers.ChangePasswordRequest.NewPassword">
            <summary>
            新密码
            </summary>
        </member>
        <member name="P:SSIC.Modules.Auth.Controllers.ChangePasswordRequest.ConfirmNewPassword">
            <summary>
            确认新密码
            </summary>
        </member>
        <member name="T:SSIC.Modules.Auth.Controllers.RoleController">
            <summary>
            角色管理控制器
            </summary>
        </member>
        <member name="M:SSIC.Modules.Auth.Controllers.RoleController.GetRoles">
            <summary>
            获取所有角色
            </summary>
            <returns>角色列表</returns>
        </member>
        <member name="M:SSIC.Modules.Auth.Controllers.RoleController.Test">
            <summary>
            测试方法 - 简单返回字符串
            </summary>
            <returns>测试消息</returns>
        </member>
        <member name="M:SSIC.Modules.Auth.Controllers.RoleController.GetRole(System.Int32)">
            <summary>
            根据ID获取角色
            </summary>
            <param name="id">角色ID</param>
            <returns>角色信息</returns>
        </member>
        <member name="M:SSIC.Modules.Auth.Controllers.RoleController.CreateRole(SSIC.Modules.Auth.Controllers.CreateRoleRequest)">
            <summary>
            创建新角色
            </summary>
            <param name="request">创建角色请求</param>
            <returns>创建结果</returns>
        </member>
        <member name="M:SSIC.Modules.Auth.Controllers.RoleController.UpdateRole(System.Int32,SSIC.Modules.Auth.Controllers.UpdateRoleRequest)">
            <summary>
            更新角色
            </summary>
            <param name="id">角色ID</param>
            <param name="request">更新角色请求</param>
            <returns>更新结果</returns>
        </member>
        <member name="M:SSIC.Modules.Auth.Controllers.RoleController.DeleteRole(System.Int32)">
            <summary>
            删除角色
            </summary>
            <param name="id">角色ID</param>
            <returns>删除结果</returns>
        </member>
        <member name="M:SSIC.Modules.Auth.Controllers.RoleController.GetRolePermissions(System.Int32)">
            <summary>
            获取角色权限
            </summary>
            <param name="id">角色ID</param>
            <returns>权限列表</returns>
        </member>
        <member name="T:SSIC.Modules.Auth.Controllers.Role">
            <summary>
            角色模型
            </summary>
        </member>
        <member name="P:SSIC.Modules.Auth.Controllers.Role.Id">
            <summary>
            角色ID
            </summary>
        </member>
        <member name="P:SSIC.Modules.Auth.Controllers.Role.Name">
            <summary>
            角色名称
            </summary>
        </member>
        <member name="P:SSIC.Modules.Auth.Controllers.Role.Description">
            <summary>
            角色描述
            </summary>
        </member>
        <member name="P:SSIC.Modules.Auth.Controllers.Role.CreatedAt">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:SSIC.Modules.Auth.Controllers.Role.UpdatedAt">
            <summary>
            更新时间
            </summary>
        </member>
        <member name="T:SSIC.Modules.Auth.Controllers.CreateRoleRequest">
            <summary>
            创建角色请求模型
            </summary>
        </member>
        <member name="P:SSIC.Modules.Auth.Controllers.CreateRoleRequest.Name">
            <summary>
            角色名称
            </summary>
        </member>
        <member name="P:SSIC.Modules.Auth.Controllers.CreateRoleRequest.Description">
            <summary>
            角色描述
            </summary>
        </member>
        <member name="T:SSIC.Modules.Auth.Controllers.UpdateRoleRequest">
            <summary>
            更新角色请求模型
            </summary>
        </member>
        <member name="P:SSIC.Modules.Auth.Controllers.UpdateRoleRequest.Name">
            <summary>
            角色名称
            </summary>
        </member>
        <member name="P:SSIC.Modules.Auth.Controllers.UpdateRoleRequest.Description">
            <summary>
            角色描述
            </summary>
        </member>
    </members>
</doc>
