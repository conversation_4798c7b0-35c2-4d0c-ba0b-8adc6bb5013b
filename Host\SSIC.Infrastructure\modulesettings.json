﻿{
  "ModulePath": {
    "Enabled": false, //是否启用自定义模块路径功能，false时使用AppContext.BaseDirectory
    "DevelopmentBasePath": "F:\\SSIC\\Host", //开发环境基础路径，调试时使用此路径避免指向调试输出目录
    "ProductionBasePath": "", //生产环境基础路径，为空时使用AppContext.BaseDirectory
    "ModulePath": "Modules", //模块相对路径，相对于基础路径的子目录名称
    "ScanSubFolders": true, //是否扫描子文件夹，true=扫描符合前缀的子目录，false=直接在根目录扫描DLL
    "ModuleDirectoryPrefix": "SSIC.Modules.", //模块目录前缀，用于过滤符合条件的子目录（仅当ScanSubFolders=true时生效）
    "ScanMode": 3, //扫描模式：0=按前缀匹配，1=按目录结构，2=按文件模式，3=通配符模式，4=所有DLL
    "WildcardPatterns": [
      "SSIC.Modules.*.dll" //通配符模式列表，用于匹配模块文件名（支持*通配符）
    ],
    "SearchOption": "TopDirectoryOnly" //搜索选项：TopDirectoryOnly=仅顶级目录，AllDirectories=递归搜索所有子目录
  }
}
